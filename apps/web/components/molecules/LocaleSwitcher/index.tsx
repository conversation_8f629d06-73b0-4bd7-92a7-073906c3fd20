"use client";

import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";
import { Button } from "@/components/atoms/Button";

const locales = ["en", "th"] as const;
type Locale = (typeof locales)[number];

export function LocaleSwitcher() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const switchLocale = (newLocale: Locale) => {
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000; SameSite=Lax`;

    const pathWithoutLocale = pathname.replace(
      new RegExp(`^/(${locales.join("|")})`),
      ""
    );

    router.push(`/${newLocale}${pathWithoutLocale}`);
    router.refresh();
  };

  return (
    <div className="flex items-center space-x-2">
      <Button
        onClick={() => switchLocale("en")}
        variant={locale === "en" ? "default" : "outline"}
        size="sm"
        disabled={locale === "en"}
        aria-label="Switch to English"
      >
        EN
      </Button>
      <Button
        onClick={() => switchLocale("th")}
        variant={locale === "th" ? "default" : "outline"}
        size="sm"
        disabled={locale === "th"}
        aria-label="Switch to Thai language"
      >
        ไทย
      </Button>
    </div>
  );
}

export default LocaleSwitcher;
