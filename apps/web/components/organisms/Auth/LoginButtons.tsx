"use client";

import { useSession } from "next-auth/react";
import { GoogleSignInButton } from "./GoogleSignInButton";
import { LinkedInSignInButton } from "./LinkedInSignInButton";
import { UserProfile } from "./UserProfile";

export function LoginButtons() {
  const { data: session, status } = useSession();

  if (status === "authenticated" && session) {
    return <UserProfile />;
  }

  return (
    <div className="space-y-4 mt-6 max-w-sm mx-auto">
      <h2 className="text-xl font-semibold mb-4 text-center bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
        Sign in with
      </h2>
      <GoogleSignInButton className="h-11" />
      <LinkedInSignInButton className="h-11" />
    </div>
  );
}

export default LoginButtons;
