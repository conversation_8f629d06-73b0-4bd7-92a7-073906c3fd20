"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  GoogleSignInButton,
  LinkedInSignInButton,
} from "@/components/organisms/Auth";

export default function RootPage() {
  const router = useRouter();
  const [showLogin, setShowLogin] = useState(false);

  useEffect(() => {
    // Show login options after a brief delay
    const timer = setTimeout(() => {
      setShowLogin(true);
    }, 500);

    // Cleanup timeout
    return () => clearTimeout(timer);
  }, []);

  const handleContinue = () => {
    // Redirect to the default locale
    router.replace("/en");
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
        <div className="text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mx-auto mb-4 flex items-center justify-center">
              <span className="text-2xl font-bold text-white">C</span>
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Welcome to CheckU
            </h1>
          </div>
          {!showLogin ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
              <div
                className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"
                style={{ animationDelay: "0.1s" }}
              ></div>
              <div
                className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                style={{ animationDelay: "0.2s" }}
              ></div>
              <p className="text-gray-600 ml-3">Loading options...</p>
            </div>
          ) : (
            <div className="space-y-6">
              <p className="text-gray-600 text-lg">
                Sign in to continue or proceed as a guest
              </p>

              <div className="space-y-4">
                <GoogleSignInButton className="h-12" />

                <LinkedInSignInButton className="h-12" />

                <button
                  onClick={handleContinue}
                  className="w-full h-12 flex items-center justify-center px-4 border border-gray-200 rounded-lg shadow-sm text-sm font-medium text-gray-600 bg-gray-50/50 hover:bg-gray-100/80 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400 transition-all duration-200 group"
                >
                  <span className="group-hover:scale-105 transition-transform duration-200">
                    Continue as Guest
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
