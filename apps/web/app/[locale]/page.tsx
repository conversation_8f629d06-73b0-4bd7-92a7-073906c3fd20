"use client";

import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import LocaleSwitcher from "@/components/LocaleSwitcher";
import { LoginButtons } from "@/components";

export default function HomePage() {
  const t = useTranslations("common");
  const { status } = useSession();

  // Add error boundary or null checks if needed
  if (!t) {
    return <div>Loading translations...</div>;
  }

  return (
    <main className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold">{t("welcome")}</h1>
          <div className="flex items-center space-x-4">
            <LocaleSwitcher />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <p className="text-lg">
              {status === "authenticated"
                ? t("welcomeBack") || "Welcome back to CheckU!"
                : t("welcome")}
            </p>
            <p>Current language: {t("language")}</p>

            <div className="mt-8 space-y-4">
              <h2 className="text-xl font-semibold">Navigation</h2>
              <ul className="space-y-2">
                <li>{t("home")}</li>
                <li>{t("about")}</li>
                <li>{t("contact")}</li>
              </ul>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-md">
            <LoginButtons />
          </div>
        </div>
      </div>
    </main>
  );
}
