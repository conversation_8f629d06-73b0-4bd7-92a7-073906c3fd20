import {
  GoogleSignInButton,
  LinkedInSignInButton,
} from "@/components/organisms/Auth";

export default function SignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
            <span className="text-2xl font-bold text-white">C</span>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            Sign in to your account
          </h2>
          <p className="mt-4 text-gray-600">
            Welcome back! Please sign in to continue.
          </p>
        </div>
        <div className="mt-8 space-y-4">
          <GoogleSignInButton className="h-12" />
          <LinkedInSignInButton className="h-12" />
        </div>
      </div>
    </div>
  );
}
